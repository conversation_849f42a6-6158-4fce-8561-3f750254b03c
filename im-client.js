// XMPP IM客户端 - 所有JavaScript功能 + 增强加密登录
// XMPP连接配置
const WEBSOCKET_URL = 'wss://im-8845.dev.internal.nglock.com/ws';

let websocket = null;
let currentContact = '系统消息';
let currentContactJid = 'system';
let messages = {
    'system': [
        { text: '正在连接到XMPP服务器...', type: 'received', time: '刚刚' }
    ]
};
let currentUser = '';
let currentJid = '';
let contacts = new Set();
let isAuthenticated = false;
let debugMode = false;
let currentRetry = 1;
let maxConnectionAttempts = 3;
let loginCredentials = null;

// SCRAM认证状态
let scramState = null;

// 🔐 安全增强 - 加密和会话管理
let securityConfig = {
    encryptionEnabled: true,
    sessionKey: null,
    passwordHashed: null,
    saltForLocalStorage: null,
    maxLoginAttempts: 3,
    loginAttempts: 0,
    lockoutTime: 300000, // 5分钟锁定
    lockoutTimestamp: null,
    sessionTimeout: 3600000, // 1小时会话超时
    sessionStartTime: null,
    minimumPasswordLength: 6, // 降低最小长度要求
    requireStrongPassword: false // 关闭强密码要求，允许简单密码登录
};

// 🔐 密码加密和安全工具函数

/**
 * 生成安全的随机盐值
 */
function generateSecureSalt(length = 32) {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
}

/**
 * 使用PBKDF2生成密钥用于本地加密
 */
async function generateEncryptionKey(password, salt, iterations = 100000) {
    const passwordBuffer = new TextEncoder().encode(password);
    const saltBuffer = new TextEncoder().encode(salt);
    
    const keyMaterial = await crypto.subtle.importKey(
        'raw',
        passwordBuffer,
        'PBKDF2',
        false,
        ['deriveBits']
    );
    
    const key = await crypto.subtle.deriveBits(
        {
            name: 'PBKDF2',
            salt: saltBuffer,
            iterations: iterations,
            hash: 'SHA-256'
        },
        keyMaterial,
        256 // 32字节密钥
    );
    
    return crypto.subtle.importKey(
        'raw',
        key,
        { name: 'AES-GCM' },
        false,
        ['encrypt', 'decrypt']
    );
}

/**
 * 使用AES-GCM加密数据
 */
async function encryptData(data, key) {
    const iv = crypto.getRandomValues(new Uint8Array(12)); // 96位IV用于GCM
    const encodedData = new TextEncoder().encode(data);
    
    const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv: iv },
        key,
        encodedData
    );
    
    // 返回IV + 加密数据的组合
    const result = new Uint8Array(iv.length + encrypted.byteLength);
    result.set(iv);
    result.set(new Uint8Array(encrypted), iv.length);
    
    return btoa(String.fromCharCode(...result));
}

/**
 * 使用AES-GCM解密数据
 */
async function decryptData(encryptedData, key) {
    const data = new Uint8Array(atob(encryptedData).split('').map(c => c.charCodeAt(0)));
    
    const iv = data.slice(0, 12);
    const encrypted = data.slice(12);
    
    const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv: iv },
        key,
        encrypted
    );
    
    return new TextDecoder().decode(decrypted);
}

/**
 * 验证密码强度
 */
function validatePasswordStrength(password) {
    const errors = [];
    
    if (password.length < securityConfig.minimumPasswordLength) {
        errors.push(`密码长度至少${securityConfig.minimumPasswordLength}位`);
    }
    
    if (securityConfig.requireStrongPassword) {
        if (!/[a-z]/.test(password)) {
            errors.push('密码必须包含小写字母');
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('密码必须包含大写字母');
        }
        if (!/[0-9]/.test(password)) {
            errors.push('密码必须包含数字');
        }
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('密码必须包含特殊字符');
        }
    }
    
    // 检查常见弱密码
    const commonPasswords = ['password', '123456', 'qwerty', 'abc123', 'password123'];
    if (commonPasswords.includes(password.toLowerCase())) {
        errors.push('不能使用常见弱密码');
    }
    
    return {
        isValid: errors.length === 0,
        errors: errors,
        score: calculatePasswordScore(password)
    };
}

/**
 * 计算密码强度分数
 */
function calculatePasswordScore(password) {
    let score = 0;
    
    // 长度加分
    score += Math.min(password.length * 2, 20);
    
    // 字符类型加分
    if (/[a-z]/.test(password)) score += 5;
    if (/[A-Z]/.test(password)) score += 5;
    if (/[0-9]/.test(password)) score += 5;
    if (/[^a-zA-Z0-9]/.test(password)) score += 10;
    
    // 复杂度加分
    if (password.length >= 12) score += 10;
    if (/(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])/.test(password)) score += 10;
    
    return Math.min(score, 100);
}

/**
 * 安全的密码哈希（用于本地验证，不用于网络传输）
 */
async function hashPasswordSecurely(password, salt) {
    const combined = password + salt + 'XMPP_CLIENT_SALT';
    const hashBuffer = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(combined));
    return btoa(String.fromCharCode(...new Uint8Array(hashBuffer)));
}

/**
 * 检查是否处于登录锁定状态
 */
function isLoginLocked() {
    if (!securityConfig.lockoutTimestamp) return false;
    
    const now = Date.now();
    if (now - securityConfig.lockoutTimestamp > securityConfig.lockoutTime) {
        // 锁定时间已过，重置
        securityConfig.lockoutTimestamp = null;
        securityConfig.loginAttempts = 0;
        return false;
    }
    
    return true;
}

/**
 * 记录登录失败
 */
function recordLoginFailure() {
    securityConfig.loginAttempts++;
    
    if (securityConfig.loginAttempts >= securityConfig.maxLoginAttempts) {
        securityConfig.lockoutTimestamp = Date.now();
        const lockoutMinutes = Math.round(securityConfig.lockoutTime / 60000);
        addErrorMessage(`❌ 登录失败次数过多，账户已锁定 ${lockoutMinutes} 分钟`);
        return true;
    }
    
    const remainingAttempts = securityConfig.maxLoginAttempts - securityConfig.loginAttempts;
    addErrorMessage(`❌ 登录失败，还有 ${remainingAttempts} 次尝试机会`);
    return false;
}

/**
 * 重置登录尝试计数
 */
function resetLoginAttempts() {
    securityConfig.loginAttempts = 0;
    securityConfig.lockoutTimestamp = null;
}

/**
 * 检查会话是否有效
 */
function isSessionValid() {
    if (!securityConfig.sessionStartTime) return false;
    
    const now = Date.now();
    return (now - securityConfig.sessionStartTime) < securityConfig.sessionTimeout;
}

/**
 * 开始新会话
 */
function startNewSession() {
    securityConfig.sessionStartTime = Date.now();
    securityConfig.sessionKey = generateSecureSalt(32);
}

/**
 * 清理会话数据
 */
function clearSession() {
    securityConfig.sessionKey = null;
    securityConfig.passwordHashed = null;
    securityConfig.sessionStartTime = null;
    
    // 清理内存中的敏感数据
    if (loginCredentials) {
        loginCredentials.password = null;
        loginCredentials = null;
    }
    
    // 清理SCRAM状态
    if (scramState) {
        scramState.password = null;
        scramState = null;
    }
}

// 🔐 增强的登录函数
async function login() {
    console.log('🔐 login函数被调用');
    
    // 获取表单元素 - 支持隐藏表单（IM-Tiny兼容）
    let usernameEl = document.getElementById('username') || document.getElementById('hiddenUsername');
    let passwordEl = document.getElementById('password') || document.getElementById('hiddenPassword');
    let domainEl = document.getElementById('domain') || document.getElementById('hiddenDomain');
    const rememberMeEl = document.getElementById('rememberMe');
    
                console.log('📋 表单元素检查:', JSON.stringify({
        username: usernameEl ? '✅' : '❌',
        password: passwordEl ? '✅' : '❌',
        domain: domainEl ? '✅' : '❌',
        rememberMe: rememberMeEl ? '✅' : '❌'
    }));
    
    if (!usernameEl || !passwordEl || !domainEl) {
        addErrorMessage('❌ 表单元素缺失，请刷新页面重试');
        console.error('❌ 表单元素缺失');
        return;
    }
    
    const username = usernameEl.value.trim();
    const password = passwordEl.value;
    const domain = domainEl.value.trim();
    const rememberMe = rememberMeEl?.checked || false;
    
    console.log('📝 登录参数:', JSON.stringify({
        username: username || '(空)',
        passwordLength: password ? password.length : 0,
        domain: domain || '(空)',
        rememberMe: rememberMe
    }));
    
    if (!username || !password || !domain) {
        // 检查是否可以自动填充默认值
        if (!username && !password && !domain) {
            setDefaultCredentials();
            // 重新获取值
            username = usernameEl.value.trim();
            password = passwordEl.value;
            domain = domainEl.value.trim();
        }
        
        // 如果仍然为空，则报错
        if (!username || !password || !domain) {
            const errorMsg = '请填写完整的登录信息';
            console.log('❌ 登录信息不完整');
            
            // 尝试显示错误信息
            try {
                addErrorMessage(errorMsg);
            } catch (e) {
                console.error('❌ addErrorMessage函数调用失败:', e);
                alert(errorMsg); // 回退到alert
            }
            return;
        }
    }
    
    // 检查登录锁定
    if (isLoginLocked()) {
        const remainingTime = Math.ceil((securityConfig.lockoutTime - (Date.now() - securityConfig.lockoutTimestamp)) / 60000);
        addErrorMessage(`❌ 账户已锁定，请 ${remainingTime} 分钟后重试`);
        return;
    }
    
            // 验证密码强度（仅在启用强密码要求时阻止登录）
        const passwordValidation = validatePasswordStrength(password);
        if (!passwordValidation.isValid && securityConfig.requireStrongPassword) {
            addErrorMessage('❌ 密码强度不足:');
            passwordValidation.errors.forEach(error => addErrorMessage('  • ' + error));
            return;
        }
        
        // 如果不要求强密码但密码很弱，给出友好提示（不阻止登录）
        if (!passwordValidation.isValid && !securityConfig.requireStrongPassword) {
            addSystemMessage(`💡 密码强度: ${passwordValidation.score}/100 分`);
            if (passwordValidation.score < 40) {
                addSystemMessage('💡 建议使用更强的密码以提高安全性');
            }
        }
    
    try {
        console.log('🔐 开始验证登录信息...');
        
        // 尝试显示系统消息
        try {
            addSystemMessage(`🔐 正在验证登录信息...`);
        } catch (e) {
            console.warn('⚠️ addSystemMessage函数调用失败:', e);
        }
        
        // 生成本地加密盐值（如果不存在）
        if (!securityConfig.saltForLocalStorage) {
            securityConfig.saltForLocalStorage = generateSecureSalt();
        }
        
        // 对密码进行本地哈希（用于验证，不用于网络传输）
        securityConfig.passwordHashed = await hashPasswordSecurely(password, securityConfig.saltForLocalStorage);
        
        console.log('开始安全登录过程:', { 
            username, 
            domain, 
            passwordScore: passwordValidation.score,
            rememberMe 
        });
        
        // 如果选择记住密码，使用加密存储
        if (rememberMe && securityConfig.encryptionEnabled) {
            try {
                const encryptionKey = await generateEncryptionKey(password, securityConfig.saltForLocalStorage);
                const encryptedCredentials = await encryptData(JSON.stringify({
                    username,
                    domain,
                    timestamp: Date.now()
                }), encryptionKey);
                
                localStorage.setItem('encryptedCredentials', encryptedCredentials);
                localStorage.setItem('credentialsSalt', securityConfig.saltForLocalStorage);
                addSystemMessage('💾 登录信息已安全保存');
            } catch (error) {
                console.warn('保存加密凭据失败:', error);
                addSystemMessage('⚠️ 无法保存登录信息');
            }
        }
        
        // 保存登录凭据以便重连使用（内存中临时存储）
        loginCredentials = { username, password, domain };
        
        // 重置连接计数器和登录失败计数
        currentRetry = 1;
        
        // 开始新的安全会话
        startNewSession();
        
        // 隐藏登录表单（如果存在）
        const loginFormEl = document.getElementById('loginForm');
        if (loginFormEl) {
            loginFormEl.style.display = 'none';
        }
        
        // 显示聊天界面（如果存在）
        const chatAppEl = document.getElementById('chatApp');
        if (chatAppEl) {
            chatAppEl.classList.remove('hidden');
        }
        
        // 确保界面切换完成后再进行后续操作
        setTimeout(() => {
            console.log('界面切换完成，开始安全连接...');
            
            // 初始化系统消息并切换到系统消息
            selectContact('系统消息', 'system');
            
            // 清空之前的消息
            messages['system'] = [];
            
            // 只在调试模式或原始界面显示详细信息
            if (debugMode || !document.getElementById('chatScreen')) {
                addSystemMessage(`👤 正在安全登录 ${username}@${domain}`);
                addSystemMessage(`🔐 密码强度: ${passwordValidation.score}/100 分`);
            }
            
            // 再次延迟确保消息显示
            setTimeout(() => {
                console.log('开始连接到XMPP服务器...');
                connectToXMPP(username, password, domain);
            }, 100);
            
        }, 200);
        
    } catch (error) {
        console.error('登录过程出错:', error);
        addErrorMessage('❌ 登录失败: ' + error.message);
        recordLoginFailure();
    }
}

// 生成随机ID
function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

// 转义XML特殊字符
function escapeXml(unsafe) {
    return unsafe.replace(/[<>&'"]/g, function (c) {
        switch (c) {
            case '<': return '&lt;';
            case '>': return '&gt;';
            case '&': return '&amp;';
            case '\'': return '&apos;';
            case '"': return '&quot;';
        }
    });
}

// 解析XML
function parseXml(xmlString) {
    const parser = new DOMParser();
    return parser.parseFromString(xmlString, 'text/xml');
}

function connectToXMPP(username, password, domain) {
    console.log('connectToXMPP函数被调用:', { username, domain });
    
    try {
        // 只在调试模式或原始界面显示连接详情
        if (debugMode || !document.getElementById('chatScreen')) {
            addSystemMessage(`🔄 正在连接到 ${domain}...`);
        }
        updateConnectionStatus('正在连接...', 'connecting');
        
        console.log('创建WebSocket连接到:', 'wss://im-8845.dev.internal.nglock.com/ws');
        
        websocket = new WebSocket('wss://im-8845.dev.internal.nglock.com/ws');
        console.log('WebSocket对象已创建');
        
        websocket.onopen = function() {
            console.log('WebSocket连接已建立');
            // 只在调试模式显示连接详情
            if (debugMode) {
                addSystemMessage('✅ 连接已建立');
            }
            
            // 发送WebSocket framing open标签
            const openStanza = `<open to='${domain}' version='1.0' xmlns='urn:ietf:params:xml:ns:xmpp-framing'/>`;
            websocket.send(openStanza);
            
            if (debugMode) {
                addDebugMessage('发送 ► ' + openStanza);
            }
        };
        
        websocket.onmessage = function(event) {
            console.log('收到WebSocket消息:', event.data);
            if (debugMode) {
                addDebugMessage('接收 ◄ ' + event.data);
            }
            handleXMPPMessage(event.data, username, password, domain);
        };
        
        websocket.onclose = function(event) {
            console.log('WebSocket连接关闭:', event.code, event.reason);
            const reason = event.reason || '连接断开';
            addSystemMessage(`❌ ${reason}`);
            updateConnectionStatus('连接已断开', 'offline');
            isAuthenticated = false;
            
            // 显示重新连接按钮
            document.getElementById('reconnectBtn').classList.remove('hidden');
        };
        
        websocket.onerror = function(error) {
            console.log('WebSocket连接错误:', error);
            addErrorMessage('❌ 连接错误，请检查网络');
            updateConnectionStatus('连接错误', 'offline');
        };
        
        // 设置连接超时
        setTimeout(() => {
            if (websocket && websocket.readyState === WebSocket.CONNECTING) {
                console.log('WebSocket连接超时');
                addErrorMessage('❌ 连接超时，正在重试...');
                websocket.close();
            }
        }, 10000); // 10秒超时
        
    } catch (error) {
        console.log('connectToXMPP函数出错:', error);
        addErrorMessage('❌ 连接失败: ' + error.message);
        updateConnectionStatus('连接失败', 'offline');
    }
}

// ========== SCRAM认证功能 ==========

// 生成随机字符串（用于SCRAM nonce）
function generateRandomString(length = 20) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// 生成SCRAM客户端nonce（Base64编码的随机字节）
function generateScramNonce() {
    // 生成16字节的随机数据（与Converse.js一致）
    const randomBytes = new Uint8Array(16);
    crypto.getRandomValues(randomBytes);
    // 转换为Base64
    return btoa(String.fromCharCode(...randomBytes));
}

// 改进的密码标准化函数（SASLprep）
function saslPrep(str) {
    // 基本的SASLprep实现
    // 1. 映射 - 移除一些字符
    // 2. 标准化 - Unicode标准化
    // 3. 禁止字符检查
    // 4. 双向字符检查
    
    // 简化版本：移除首尾空格，进行基本的Unicode标准化
    let result = str.trim();
    
    // 如果浏览器支持，进行Unicode标准化
    if (String.prototype.normalize) {
        result = result.normalize('NFKC');
    }
    
    return result;
}

// 修复Base64编码函数以正确处理UTF-8
function stringToBase64(str) {
    // 确保正确处理UTF-8字符
    return btoa(unescape(encodeURIComponent(str)));
}

// 修复Base64解码函数以正确处理UTF-8
function base64ToString(str) {
    // 确保正确处理UTF-8字符
    return decodeURIComponent(escape(atob(str)));
}

// 字符串转ArrayBuffer - 确保UTF-8编码
function stringToArrayBuffer(str) {
    return new TextEncoder('utf-8').encode(str);
}

// ArrayBuffer转字符串
function arrayBufferToString(buffer) {
    const decoder = new TextDecoder();
    return decoder.decode(buffer);
}

// HMAC-SHA计算
async function hmacSha(algorithm, key, data) {
    if (!window.crypto || !window.crypto.subtle) {
        throw new Error('Web Crypto API不可用');
    }
    
    const keyBuffer = typeof key === 'string' ? stringToArrayBuffer(key) : key;
    const dataBuffer = typeof data === 'string' ? stringToArrayBuffer(data) : data;
    
    const cryptoKey = await crypto.subtle.importKey(
        'raw',
        keyBuffer,
        { name: 'HMAC', hash: algorithm },
        false,
        ['sign']
    );
    
    const signature = await crypto.subtle.sign('HMAC', cryptoKey, dataBuffer);
    return new Uint8Array(signature);
}

// PBKDF2派生密钥
async function pbkdf2(password, salt, iterations, algorithm) {
    if (!window.crypto || !window.crypto.subtle) {
        throw new Error('Web Crypto API不可用');
    }
    
    const passwordBuffer = stringToArrayBuffer(password);
    const saltBuffer = typeof salt === 'string' ? stringToArrayBuffer(salt) : salt;
    
    const keyMaterial = await crypto.subtle.importKey(
        'raw',
        passwordBuffer,
        'PBKDF2',
        false,
        ['deriveBits']
    );
    
    // 根据哈希算法确定输出长度
    let keyLength;
    switch (algorithm) {
        case 'SHA-1':
            keyLength = 160; // 20 bytes
            break;
        case 'SHA-256':
            keyLength = 256; // 32 bytes
            break;
        case 'SHA-512':
            keyLength = 512; // 64 bytes
            break;
        default:
            keyLength = 256; // 默认32字节
    }
    
    const derived = await crypto.subtle.deriveBits(
        {
            name: 'PBKDF2',
            salt: saltBuffer,
            iterations: iterations,
            hash: algorithm
        },
        keyMaterial,
        keyLength
    );
    
    return new Uint8Array(derived);
}

// XOR两个ArrayBuffer
function xorBuffers(a, b) {
    const result = new Uint8Array(a.length);
    for (let i = 0; i < a.length; i++) {
        result[i] = a[i] ^ b[i];
    }
    return result;
}

// 处理SCRAM挑战
async function handleScramChallenge(challenge, username, password, mechanism) {
    try {
        // 🔧 检查Web Crypto API可用性
        if (!window.crypto || !window.crypto.subtle) {
            throw new Error('此浏览器不支持Web Crypto API，或页面未在安全上下文中运行。请使用HTTPS或localhost访问。');
        }
        
        console.log('处理SCRAM挑战:', challenge);
        const decodedChallenge = base64ToString(challenge);
        console.log('解码的挑战:', decodedChallenge);
        
        // 解析挑战 - 修复Base64值被截断的问题
        const challengeParts = {};
        decodedChallenge.split(',').forEach(part => {
            const equalIndex = part.indexOf('=');
            if (equalIndex > 0) {
                const key = part.substring(0, equalIndex);
                const value = part.substring(equalIndex + 1);
                challengeParts[key] = value;
            }
        });
        
        console.log('挑战组件:', challengeParts);
        
        const serverNonce = challengeParts.r;
        const salt = challengeParts.s;
        const iterations = parseInt(challengeParts.i);
        const serverHash = challengeParts.h; // SCRAM Channel Binding hash
        
        if (!serverNonce || !salt || !iterations) {
            throw new Error('挑战格式无效');
        }
        
        // 验证服务器nonce包含客户端nonce
        if (!serverNonce.startsWith(scramState.clientNonce)) {
            throw new Error('服务器nonce无效');
        }
        
        console.log('Server nonce (完整):', serverNonce);
        console.log('Client nonce:', scramState.clientNonce);
        console.log('Server hash (Channel Binding):', serverHash);
        
        // 标准化密码
        const normalizedPassword = saslPrep(password);
        console.log('原始密码长度:', password.length, '标准化后长度:', normalizedPassword.length);
        
        // 🔧 关键修复：正确构建server-first-message
        // 根据测试结果，如果服务器发送了h=参数，必须包含在AuthMessage中
        let serverFirstMessage;
        if (serverHash) {
            // 包含h=参数的完整server-first-message
            serverFirstMessage = `r=${serverNonce},s=${salt},i=${iterations},h=${serverHash}`;
            console.log('Server first message (含h=):', serverFirstMessage);
        } else {
            // 标准格式（不含h=）
            serverFirstMessage = `r=${serverNonce},s=${salt},i=${iterations}`;
            console.log('Server first message (标准):', serverFirstMessage);
        }
        
        // 准备认证算法
        let hashAlgorithm;
        switch (mechanism) {
            case 'SCRAM-SHA-1':
                hashAlgorithm = 'SHA-1';
                break;
            case 'SCRAM-SHA-256':
                hashAlgorithm = 'SHA-256';
                break;
            case 'SCRAM-SHA-512':
                hashAlgorithm = 'SHA-512';
                break;
            default:
                throw new Error('不支持的SCRAM机制: ' + mechanism);
        }
        
        console.log('使用哈希算法:', hashAlgorithm);
        
        // 解码salt
        const saltBuffer = new Uint8Array(atob(salt).split('').map(c => c.charCodeAt(0)));
        
        // 计算SaltedPassword
        const saltedPassword = await pbkdf2(normalizedPassword, saltBuffer, iterations, hashAlgorithm);
        console.log('SaltedPassword计算完成');
        
        // 计算ClientKey和ServerKey
        const clientKey = await hmacSha(hashAlgorithm, saltedPassword, 'Client Key');
        const serverKey = await hmacSha(hashAlgorithm, saltedPassword, 'Server Key');
        
        // 计算StoredKey
        const storedKey = new Uint8Array(await crypto.subtle.digest(hashAlgorithm, clientKey));
        
        // 构建AuthMessage - 🔧 使用修复后的逻辑
        // channel binding: 'n,,' = 不支持channel binding（完整的GS2头部）
        const gs2Header = 'n,,';  
        const clientFinalWithoutProof = `c=${btoa(gs2Header)},r=${serverNonce}`;
        
        // 🔧 关键修复：使用正确的AuthMessage构建
        // AuthMessage = client-first-message-bare + "," + server-first-message + "," + client-final-message-without-proof
        const authMessage = `${scramState.clientFirstMessageBare},${serverFirstMessage},${clientFinalWithoutProof}`;
        
        console.log('AuthMessage:', authMessage);
        console.log('Client-first-message-bare:', scramState.clientFirstMessageBare);
        console.log('Server-first-message:', serverFirstMessage);
        console.log('Client-final-without-proof:', clientFinalWithoutProof);
        console.log('GS2 header for c parameter:', gs2Header);
        console.log('Base64 of GS2 header:', btoa(gs2Header));
        
        if (debugMode) {
            addDebugMessage('=== SCRAM认证详情 ===');
            addDebugMessage('机制: ' + mechanism);
            addDebugMessage('AuthMessage长度: ' + authMessage.length + ' 字符');
            addDebugMessage('AuthMessage: ' + authMessage);
        }
        
        // 计算ClientSignature和ClientProof
        const clientSignature = await hmacSha(hashAlgorithm, storedKey, authMessage);
        const clientProof = xorBuffers(clientKey, clientSignature);
        
        // 计算ServerSignature用于验证
        const serverSignature = await hmacSha(hashAlgorithm, serverKey, authMessage);
        
        // 保存ServerSignature用于后续验证
        scramState.expectedServerSignature = btoa(String.fromCharCode(...serverSignature));
        
        // 构建客户端final消息
        const clientProofB64 = btoa(String.fromCharCode(...clientProof));
        const clientFinal = `${clientFinalWithoutProof},p=${clientProofB64}`;
        
        console.log('客户端final消息:', clientFinal);
        console.log('Client proof (base64):', clientProofB64);
        
        // 发送响应
        const responseB64 = stringToBase64(clientFinal);
        const responseMessage = `<response xmlns='urn:ietf:params:xml:ns:xmpp-sasl'>${responseB64}</response>`;
        
        if (debugMode) {
            addDebugMessage(`发送SCRAM响应 ► ${responseMessage}`);
        }
        websocket.send(responseMessage);
        
    } catch (error) {
        console.error('SCRAM挑战处理失败:', error);
        addErrorMessage('❌ 认证失败: ' + error.message);
    }
}

function handleXMPPMessage(data, username, password, domain) {
    try {
        let xmlData = data;
        console.log('处理XMPP消息:', xmlData);
        
        // 处理WebSocket framing open标签
        if (xmlData.includes('<open xmlns=\'urn:ietf:params:xml:ns:xmpp-framing\'')) {
            if (debugMode) {
                addDebugMessage('收到WebSocket open响应');
            }
            return;
        }
        
        // 如果收到流特性
        if (xmlData.includes('<stream:features') || xmlData.includes('<features')) {
            console.log('收到流特性，开始处理...');
            
            // 检查是否已经认证：如果包含bind元素，说明已认证，不需要再做SASL认证
            if (xmlData.includes('<bind') && !isAuthenticated) {
                addSystemMessage('🔗 正在绑定资源...');
                const bindId = generateId();
                const resource = `webclient-${Date.now()}`;
                const bindStanza = `<iq type='set' id='${bindId}'><bind xmlns='urn:ietf:params:xml:ns:xmpp-bind'><resource>${resource}</resource></bind></iq>`;
                websocket.send(bindStanza);
                
                if (debugMode) {
                    addDebugMessage('发送绑定 ► ' + bindStanza);
                }
                
                // 标记为正在进行绑定，避免重复
                isAuthenticated = 'binding';
                return;
            }
            
            // 只有在未认证时才寻找认证机制
            if (isAuthenticated) {
                console.log('已认证，跳过认证机制处理');
                return;
            }
            
            // 解析支持的认证机制
            const mechanisms = [];
            const mechanismMatches = xmlData.match(/<mechanism>([^<]+)<\/mechanism>/g);
            console.log('机制匹配结果:', mechanismMatches);
            
            if (mechanismMatches) {
                mechanismMatches.forEach(match => {
                    const mechanism = match.replace(/<\/?mechanism>/g, '');
                    mechanisms.push(mechanism);
                });
            }
            
            console.log('解析出的认证机制:', mechanisms);
            if (debugMode) {
                addSystemMessage('🔐 支持的认证机制: ' + mechanisms.join(', '));
            }
            
            // 修改认证机制优先级 - 检查环境支持
            let selectedMechanism = null;
            const canUseSCRAM = !!(window.crypto && window.crypto.subtle && window.isSecureContext);
            
            if (canUseSCRAM) {
                // 优先尝试SCRAM-SHA-512，因为Converse.js成功使用了这个机制
                if (mechanisms.includes('SCRAM-SHA-512')) {
                    selectedMechanism = 'SCRAM-SHA-512';
                } else if (mechanisms.includes('SCRAM-SHA-256')) {
                    selectedMechanism = 'SCRAM-SHA-256';
                } else if (mechanisms.includes('SCRAM-SHA-1')) {
                    selectedMechanism = 'SCRAM-SHA-1';
                } else if (mechanisms.includes('PLAIN')) {
                    selectedMechanism = 'PLAIN';
                }
            } else {
                // Web Crypto API不可用，只能使用PLAIN
                if (mechanisms.includes('PLAIN')) {
                    selectedMechanism = 'PLAIN';
                    addSystemMessage('⚠️ 环境限制：使用PLAIN认证（建议使用HTTPS）');
                }
            }
            
            if (selectedMechanism && selectedMechanism.startsWith('SCRAM-')) {
                console.log('选择认证机制:', selectedMechanism);
                addSystemMessage(`🔐 正在认证...`);
                
                const clientNonce = generateScramNonce();
                
                // 对用户名应用SASLprep标准化
                const normalizedUsername = saslPrep(username);
                
                // 构建client-first-message-bare（不包含GS2头部）
                const clientFirstMessageBare = `n=${normalizedUsername},r=${clientNonce}`;
                
                // 构建完整的初始消息（包含GS2头部）
                // GS2头部: 'n,,' 表示不使用channel binding
                const gs2Header = 'n,,';
                const initialMessage = gs2Header + clientFirstMessageBare;
                
                // 保存SCRAM状态
                scramState = {
                    mechanism: selectedMechanism,
                    clientNonce: clientNonce,
                    clientFirstMessageBare: clientFirstMessageBare,
                    username: normalizedUsername,
                    password: password
                };
                
                console.log('发送SCRAM初始认证:', initialMessage);
                console.log('Client-first-message-bare:', clientFirstMessageBare);
                console.log('GS2 header:', gs2Header);
                
                if (debugMode) {
                    addDebugMessage(`发送${selectedMechanism}认证 ► <auth xmlns='urn:ietf:params:xml:ns:xmpp-sasl' mechanism='${selectedMechanism}'>${stringToBase64(initialMessage)}</auth>`);
                }
                
                const authMessage = `<auth xmlns='urn:ietf:params:xml:ns:xmpp-sasl' mechanism='${selectedMechanism}'>${stringToBase64(initialMessage)}</auth>`;
                
                websocket.send(authMessage);
            } else if (selectedMechanism === 'PLAIN') {
                console.log('选择认证机制: PLAIN');
                addSystemMessage('🔐 正在认证...');
                
                // PLAIN认证：authzid\0authcid\0passwd
                const authString = `\0${username}\0${password}`;
                const authB64 = stringToBase64(authString);
                
                console.log('发送PLAIN认证');
                if (debugMode) {
                    addDebugMessage(`发送PLAIN认证 ► <auth xmlns='urn:ietf:params:xml:ns:xmpp-sasl' mechanism='PLAIN'>${authB64}</auth>`);
                }
                
                const authMessage = `<auth xmlns='urn:ietf:params:xml:ns:xmpp-sasl' mechanism='PLAIN'>${authB64}</auth>`;
                websocket.send(authMessage);
            } else {
                console.log('没有找到支持的认证机制');
                addErrorMessage('❌ 服务器不支持任何已知的认证机制');
            }
            console.log('流特性处理完成，返回');
            return;
        }
        
        // 处理SCRAM挑战
        if (xmlData.includes('<challenge')) {
            const challengeMatch = xmlData.match(/<challenge[^>]*>([^<]+)<\/challenge>/);
            if (challengeMatch && scramState) {
                const challenge = challengeMatch[1];
                addSystemMessage('🔐 正在验证身份...');
                
                if (debugMode) {
                    addDebugMessage('SCRAM挑战 (base64): ' + challenge);
                }
                
                // 异步处理SCRAM挑战（handleScramChallenge现在已经处理发送）
                handleScramChallenge(challenge, scramState.username, scramState.password, scramState.mechanism)
                    .catch(error => {
                        console.error('SCRAM挑战处理失败:', error);
                        addErrorMessage('❌ 身份验证失败: ' + error.message);
                        websocket.close();
                    });
            } else {
                addErrorMessage('❌ 认证错误');
                websocket.close();
            }
            return;
        }
        
        // 处理认证成功
        if (xmlData.includes('<success')) {
            // 🔐 增强的服务器签名验证
            let serverVerificationPassed = true;
            if (scramState && scramState.expectedServerSignature) {
                const successMatch = xmlData.match(/<success[^>]*>([^<]*)<\/success>/);
                if (successMatch && successMatch[1]) {
                    try {
                        const serverFinal = base64ToString(successMatch[1]);
                        const serverSigMatch = serverFinal.match(/v=([^,]+)/);
                        if (serverSigMatch) {
                            const serverSignature = serverSigMatch[1];
                            if (serverSignature === scramState.expectedServerSignature) {
                                addSystemMessage('🔒 服务器身份验证成功');
                                if (debugMode) {
                                    addDebugMessage('✅ 服务器签名验证通过');
                                }
                            } else {
                                serverVerificationPassed = false;
                                addErrorMessage('⚠️ 服务器身份验证失败！可能存在安全风险');
                                if (debugMode) {
                                    addDebugMessage(`❌ 服务器签名验证失败:`);
                                    addDebugMessage(`期望: ${scramState.expectedServerSignature}`);
                                    addDebugMessage(`实际: ${serverSignature}`);
                                }
                                
                                // 严格模式下拒绝连接
                                if (securityConfig.encryptionEnabled && confirm('服务器身份验证失败，是否仍要继续连接？（不推荐）')) {
                                    serverVerificationPassed = true;
                                    addSystemMessage('⚠️ 用户选择忽略服务器验证警告');
                                } else if (securityConfig.encryptionEnabled) {
                                    addErrorMessage('❌ 连接已终止，服务器身份验证失败');
                                    websocket.close();
                                    return;
                                }
                            }
                        } else {
                            addSystemMessage('ℹ️ 服务器未提供验证签名');
                        }
                    } catch (e) {
                        console.error('服务器签名验证出错:', e);
                        addErrorMessage('⚠️ 服务器身份验证过程出错');
                    }
                }
            }
            
                    // 只在调试模式显示技术细节
        if (debugMode) {
            addSystemMessage('✅ SASL认证成功');
        }
        updateConnectionStatus('正在建立安全会话...', 'connecting');
            
            // 🔐 重置登录失败计数（认证成功）
            resetLoginAttempts();
            
            // 清理敏感的SCRAM状态
            if (scramState) {
                scramState.password = null; // 清理密码
                scramState = null;
            }
            
            // 重新开始流 - 认证后必须重新建立流
            const streamHeader = `<open to='${domain}' version='1.0' xmlns='urn:ietf:params:xml:ns:xmpp-framing'/>`;
            websocket.send(streamHeader);
            
            if (debugMode) {
                addDebugMessage('发送流重启 ► ' + streamHeader);
            }
            return;
        }
        
        // 处理认证失败
        if (xmlData.includes('<failure')) {
            addErrorMessage('❌ SASL认证失败');
            updateConnectionStatus('认证失败', 'offline');
            
            // 🔐 记录登录失败并可能触发锁定
            const isLocked = recordLoginFailure();
            
            // 清理敏感数据
            clearSession();
            
            // 详细解析失败原因
            let failureReason = '';
            if (xmlData.includes('not-authorized')) {
                failureReason = '用户名或密码错误';
                addErrorMessage('❌ ' + failureReason);
            } else if (xmlData.includes('account-disabled')) {
                failureReason = '账户已被禁用';
                addErrorMessage('❌ ' + failureReason);
            } else if (xmlData.includes('credentials-expired')) {
                failureReason = '凭据已过期';
                addErrorMessage('❌ ' + failureReason);
            } else if (xmlData.includes('invalid-mechanism')) {
                failureReason = '不支持的认证机制';
                addErrorMessage('❌ ' + failureReason);
            } else if (xmlData.includes('malformed-request')) {
                failureReason = '认证请求格式错误';
                addErrorMessage('❌ ' + failureReason);
            } else if (xmlData.includes('temporary-auth-failure')) {
                failureReason = '临时认证失败，请稍后重试';
                addErrorMessage('❌ ' + failureReason);
            } else {
                failureReason = '未知认证错误';
                addErrorMessage('❌ ' + failureReason + ': ' + xmlData);
            }
            
            // 🔐 安全审计日志
            console.warn('认证失败详情:', {
                reason: failureReason,
                timestamp: new Date().toISOString(),
                attempts: securityConfig.loginAttempts,
                isLocked: isLocked,
                xmlData: xmlData
            });
            
            // 如果账户被锁定，不允许自动重连
            if (isLocked) {
                addErrorMessage('🔒 为了安全起见，请等待锁定时间结束后手动重新登录');
                // 强制回到登录界面
                setTimeout(() => {
                    document.getElementById('chatApp').classList.add('hidden');
                    document.getElementById('loginForm').style.display = 'block';
                }, 2000);
            }
            
            return;
        }
        
        // 处理认证后的流特性（包含绑定和会话）
        if (xmlData.includes('<bind') && !isAuthenticated) {
            addSystemMessage('🔗 正在绑定资源...');
            const bindId = generateId();
            const resource = `webclient-${Date.now()}`;
            const bindStanza = `<iq type='set' id='${bindId}'><bind xmlns='urn:ietf:params:xml:ns:xmpp-bind'><resource>${resource}</resource></bind></iq>`;
            websocket.send(bindStanza);
            
            if (debugMode) {
                addDebugMessage('发送绑定 ► ' + bindStanza);
            }
            
            // 标记为正在进行绑定，避免重复
            isAuthenticated = 'binding';
            return;
        }
        
        // 处理绑定结果
        if (xmlData.includes('<iq') && xmlData.includes('type=\'result\'') && xmlData.includes('<bind')) {
            const doc = parseXml(xmlData);
            const jidElement = doc.querySelector('jid');
            if (jidElement) {
                currentJid = jidElement.textContent;
                addSystemMessage(`✅ 登录成功: ${currentJid.split('@')[0]}`);
                document.getElementById('currentUser').textContent = currentJid;
                
                // 标记为已完成认证
                isAuthenticated = true;
                
                // 检查是否需要建立会话
                if (xmlData.includes('<session')) {
                    if (debugMode) {
                        addSystemMessage('🔗 建立会话...');
                    }
                    const sessionId = generateId();
                    const sessionStanza = `<iq type='set' id='${sessionId}'><session xmlns='urn:ietf:params:xml:ns:xmpp-session'/></iq>`;
                    websocket.send(sessionStanza);
                    
                    if (debugMode) {
                        addDebugMessage('发送会话 ► ' + sessionStanza);
                    }
                } else {
                    // 直接发送presence
                    sendInitialPresence();
                }
            }
        }
        
        // 处理会话建立结果
        if (xmlData.includes('<iq') && xmlData.includes('type=\'result\'') && xmlData.includes('<session')) {
            if (debugMode) {
                addSystemMessage('✅ 会话建立成功');
            }
            sendInitialPresence();
        }
        
        // 处理绑定或会话错误
        if (xmlData.includes('<iq') && xmlData.includes('type=\'error\'')) {
            if (xmlData.includes('<bind')) {
                addErrorMessage('❌ 资源绑定失败');
            } else if (xmlData.includes('<session')) {
                addErrorMessage('❌ 会话建立失败');
            }
        }
        
        // 处理收到的消息
        if (xmlData.includes('<message') && xmlData.includes('type=\'chat\'')) {
            handleIncomingMessage(xmlData);
        }
        
        // 处理presence
        if (xmlData.includes('<presence')) {
            handlePresence(xmlData);
        }
        
        // 处理流错误
        if (xmlData.includes('<stream:error>')) {
            addErrorMessage('❌ 连接错误');
            if (debugMode) {
                if (xmlData.includes('not-authorized')) {
                    addErrorMessage('流错误: 未授权');
                } else if (xmlData.includes('host-unknown')) {
                    addErrorMessage('流错误: 主机未知');
                }
            }
        }
        
        // 处理WebSocket framing close标签
        if (xmlData.includes('<close xmlns=\'urn:ietf:params:xml:ns:xmpp-framing\'')) {
            if (debugMode) {
                addSystemMessage('📋 收到WebSocket close标签');
            }
        }
        
    } catch (error) {
        console.error('处理XMPP消息错误:', error);
        addErrorMessage('处理消息时出错: ' + error.message);
    }
}

// 发送初始presence
function sendInitialPresence() {
    const presence = `<presence><show>chat</show><status>在线</status></presence>`;
    websocket.send(presence);
    
    if (debugMode) {
        addDebugMessage('发送状态 ► ' + presence);
    }
    
            isAuthenticated = true;
        updateConnectionStatus('🟢 已连接', 'online');
        
        // 简化登录成功消息
        addSystemMessage('🎉 登录成功！现在可以开始聊天了');
    
    // 隐藏重新连接按钮
    document.getElementById('reconnectBtn').classList.add('hidden');
}

// ========== UI和消息处理功能 ==========

function handleIncomingMessage(xmlData) {
    try {
        const doc = parseXml(xmlData);
        const message = doc.querySelector('message');
        if (message) {
            const from = message.getAttribute('from');
            const body = message.querySelector('body');
            
            if (body && from) {
                const fromJid = from.split('/')[0]; // 移除资源部分
                const messageText = body.textContent;
                const time = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
                
                // 添加联系人到列表
                addContactToList(fromJid);
                
                // 添加消息
                if (!messages[fromJid]) {
                    messages[fromJid] = [];
                }
                
                messages[fromJid].push({
                    text: messageText,
                    type: 'received',
                    time: time
                });
                
                // 如果当前正在查看这个联系人的聊天，刷新显示
                if (currentContactJid === fromJid) {
                    displayMessages();
                }
                
                // 通知 IM-Tiny 界面（如果存在）
                if (typeof window.handleReceivedMessageTiny === 'function') {
                    window.handleReceivedMessageTiny(fromJid, messageText, time);
                } else if (typeof window.addMessageToChat === 'function') {
                    window.addMessageToChat(messageText, 'received', time);
                }
                
                // 只在调试模式下显示详细信息
                if (debugMode) {
                    addSystemMessage(`📨 收到来自 ${fromJid} 的消息: ${messageText}`);
                }
            }
        }
    } catch (error) {
        console.error('处理收到的消息错误:', error);
    }
}

function handlePresence(xmlData) {
    try {
        const doc = parseXml(xmlData);
        const presence = doc.querySelector('presence');
        if (presence) {
            const from = presence.getAttribute('from');
            const type = presence.getAttribute('type');
            
            if (from && from !== currentJid) {
                const fromJid = from.split('/')[0]; // 移除资源部分
                const fromBareJid = fromJid;
                const currentBareJid = currentJid.split('/')[0];
                
                // 过滤掉自己的其他会话，但保留不同用户
                if (fromBareJid !== currentBareJid) {
                    if (type === 'unavailable') {
                        addSystemMessage(`👋 ${fromBareJid} 离线了`);
                    } else if (!type) {
                        addSystemMessage(`👋 发现联系人: ${fromBareJid}`);
                        addContactToList(fromBareJid);
                    }
                } else {
                    // 自己的其他会话上线/下线
                    const resource = from.split('/')[1] || '未知资源';
                    if (type === 'unavailable') {
                        if (debugMode) {
                            addSystemMessage(`📱 你的其他会话离线了: ${resource}`);
                        }
                    } else if (!type) {
                        if (debugMode) {
                            addSystemMessage(`📱 你的其他会话上线了: ${resource}`);
                        }
                        // 可以作为聊天目标，但显示更清晰的名称
                        addContactToList(from, `我的其他会话 (${resource})`);
                    }
                }
            }
        }
    } catch (error) {
        console.error('处理presence错误:', error);
    }
}

function selectContact(contactName, contactJid, element) {
    // 移除之前的active状态
    document.querySelectorAll('.contact-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 添加新的active状态
    let targetElement = element;
    if (!targetElement && typeof event !== 'undefined' && event.target) {
        targetElement = event.target.closest('.contact-item');
    }
    if (!targetElement) {
        // 如果没有element参数也没有event，查找对应的联系人项
        const contactItems = document.querySelectorAll('.contact-item');
        for (const item of contactItems) {
            const nameElement = item.querySelector('.contact-name');
            if (nameElement && nameElement.textContent === contactName) {
                targetElement = item;
                break;
            }
        }
    }
    
    if (targetElement) {
        targetElement.classList.add('active');
    }
    
    currentContact = contactName;
    currentContactJid = contactJid;
    document.getElementById('chatTitle').textContent = contactName;
    
    // 显示该联系人的消息
    displayMessages();
}

function displayMessages() {
    const chatMessages = document.getElementById('chatMessages');
    chatMessages.innerHTML = '';
    
    const contactMessages = messages[currentContactJid] || [];
    contactMessages.forEach(msg => {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${msg.type}`;
        messageDiv.innerHTML = `
            <div>${escapeXml(msg.text)}</div>
            <div class="message-time">${msg.time}</div>
        `;
        chatMessages.appendChild(messageDiv);
    });
    
    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function sendMessage() {
    const input = document.getElementById('messageInput') || document.getElementById('messageInputHidden');
    if (!input) return;
    
    const text = input.value.trim();
    
    if (!text) {
        return; // 空消息不处理
    }
    
    if (!websocket || !isAuthenticated) {
        addSystemMessage('❌ 请先登录后再发送消息');
        return;
    }
    
    if (currentContactJid === 'system') {
        addSystemMessage('❌ 无法向系统消息发送普通消息');
        addSystemMessage('💡 请选择一个真实的联系人进行聊天：');
        addSystemMessage('   • 点击左侧已有的联系人');
        addSystemMessage('   • 或点击"+ 添加联系人"添加新联系人');
        addSystemMessage('   • 联系人格式：<EMAIL>');
        input.value = ''; // 清空输入框
        return;
    }
    
    const messageId = generateId();
    const time = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
    
    // 发送XMPP消息
    const messageStanza = `<message type='chat' to='${currentContactJid}' id='${messageId}'><body>${escapeXml(text)}</body></message>`;
    
    try {
        websocket.send(messageStanza);
        
        if (debugMode) {
            addDebugMessage('发送消息 ► ' + messageStanza);
            addSystemMessage(`📤 消息已发送给 ${currentContactJid}: ${text}`);
        }
        
        // 添加到本地消息列表
        if (!messages[currentContactJid]) {
            messages[currentContactJid] = [];
        }
        
        messages[currentContactJid].push({
            text: text,
            type: 'sent',
            time: time
        });
        
        input.value = '';
        displayMessages();
        
    } catch (error) {
        addErrorMessage('❌ 发送消息失败: ' + error.message);
        if (debugMode) {
            addDebugMessage('发送错误: ' + error.stack);
        }
    }
}

function addSystemMessage(text) {
    const time = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
    
    if (!messages['system']) {
        messages['system'] = [];
    }
    
    messages['system'].push({
        text: text,
        type: 'received',
        time: time
    });
    
    if (currentContactJid === 'system') {
        displayMessages();
    }
}

function addDebugMessage(text) {
    const time = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
    
    if (!messages['system']) {
        messages['system'] = [];
    }
    
    messages['system'].push({
        text: text,
        type: 'debug',
        time: time
    });
    
    if (currentContactJid === 'system') {
        displayMessages();
    }
}

function addErrorMessage(text) {
    const time = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
    
    if (!messages['system']) {
        messages['system'] = [];
    }
    
    messages['system'].push({
        text: text,
        type: 'error',
        time: time
    });
    
    if (currentContactJid === 'system') {
        displayMessages();
    }
}

function updateConnectionStatus(status, statusType) {
    document.getElementById('connectionStatus').childNodes[0].textContent = status;
    const indicator = document.getElementById('userStatusIndicator');
    const statusText = document.getElementById('userStatusText');
    
    indicator.className = `status-indicator status-${statusType}`;
    
    switch(statusType) {
        case 'online':
            statusText.textContent = '在线';
            break;
        case 'offline':
            statusText.textContent = '离线';
            break;
        case 'connecting':
            statusText.textContent = '连接中...';
            break;
    }
}

function addContactToList(jid, displayName) {
    if (contacts.has(jid) || jid === currentJid.split('/')[0]) {
        return;
    }
    
    contacts.add(jid);
    
    const contactsDiv = document.getElementById('contactsList');
    const contactDiv = document.createElement('div');
    contactDiv.className = 'contact-item';
    contactDiv.onclick = () => selectContact(displayName || jid, jid, contactDiv);
    contactDiv.innerHTML = `
        <div class="contact-name">${displayName || jid}</div>
        <div class="contact-status">
            <span class="status-indicator status-online"></span>在线
        </div>
    `;
    
    // 在"添加联系人"按钮之前插入
    const addContactBtn = contactsDiv.querySelector('.add-contact');
    contactsDiv.insertBefore(contactDiv, addContactBtn);
}

function addContact() {
    const jid = prompt('请输入联系人JID (例如: <EMAIL>):');
    if (jid && jid.includes('@')) {
        addContactToList(jid);
        
        // 发送订阅请求
        if (websocket && isAuthenticated) {
            const subscribeStanza = `<presence type='subscribe' to='${jid}'/>`;
            websocket.send(subscribeStanza);
            addSystemMessage(`📤 已发送好友请求给 ${jid}`);
        }
    }
}

// 🔐 安全的重连功能
function reconnect() {
    // 检查登录锁定状态
    if (isLoginLocked()) {
        const remainingTime = Math.ceil((securityConfig.lockoutTime - (Date.now() - securityConfig.lockoutTimestamp)) / 60000);
        addErrorMessage(`❌ 账户已锁定，请 ${remainingTime} 分钟后重试`);
        return;
    }
    
    // 检查会话有效性
    if (!isSessionValid()) {
        addErrorMessage('❌ 会话已过期，请重新登录');
        secureLogout();
        return;
    }
    
    if (loginCredentials && currentRetry < maxConnectionAttempts) {
        addSystemMessage(`🔄 正在进行第 ${currentRetry} 次安全重连...`);
        document.getElementById('reconnectBtn').classList.add('hidden');
        
        // 验证存储的凭据哈希是否匹配（防止内存被篡改）
        if (securityConfig.passwordHashed) {
            hashPasswordSecurely(loginCredentials.password, securityConfig.saltForLocalStorage)
                .then(hash => {
                    if (hash === securityConfig.passwordHashed) {
                        addSystemMessage('✅ 凭据验证通过，开始重连');
                        connectToXMPP(loginCredentials.username, loginCredentials.password, loginCredentials.domain);
                    } else {
                        addErrorMessage('❌ 凭据验证失败，请重新登录');
                        secureLogout();
                    }
                })
                .catch(error => {
                    addErrorMessage('❌ 凭据验证出错: ' + error.message);
                    secureLogout();
                });
        } else {
            connectToXMPP(loginCredentials.username, loginCredentials.password, loginCredentials.domain);
        }
        
        currentRetry++;
    } else if (currentRetry >= maxConnectionAttempts) {
        addErrorMessage('❌ 已达到最大重连次数，请检查网络连接或服务器设置');
        addSystemMessage('💡 建议手动重新登录');
    } else {
        addErrorMessage('❌ 无法重连：缺少登录凭据');
        secureLogout();
    }
}

// 🔐 安全登出功能
function secureLogout() {
    try {
        addSystemMessage('🔒 正在安全登出...');
        
        // 发送presence unavailable（如果连接还在）
        if (websocket && websocket.readyState === WebSocket.OPEN && isAuthenticated) {
            const unavailablePresence = '<presence type="unavailable"><status>离线</status></presence>';
            websocket.send(unavailablePresence);
            
            if (debugMode) {
                addDebugMessage('发送下线状态 ► ' + unavailablePresence);
            }
        }
        
        // 关闭WebSocket连接
        if (websocket) {
            websocket.close();
            websocket = null;
        }
        
        // 清理所有安全状态
        clearSession();
        
        // 清理UI状态
        isAuthenticated = false;
        currentUser = '';
        currentJid = '';
        contacts.clear();
        messages = {
            'system': []
        };
        
        // 重置连接状态
        updateConnectionStatus('已断开连接', 'offline');
        
        // 显示登录界面
        document.getElementById('chatApp').classList.add('hidden');
        document.getElementById('loginForm').style.display = 'block';
        document.getElementById('reconnectBtn').classList.add('hidden');
        
        // 清理表单（可选）
        if (confirm('是否清除保存的登录信息？')) {
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            document.getElementById('domain').value = '';
            
            // 清理本地存储的加密凭据
            localStorage.removeItem('encryptedCredentials');
            localStorage.removeItem('credentialsSalt');
            
            addSystemMessage('🗑️ 已清除所有保存的登录信息');
        }
        
        addSystemMessage('✅ 安全登出完成');
        
    } catch (error) {
        console.error('安全登出过程出错:', error);
        addErrorMessage('⚠️ 登出过程出错，但已清理本地状态');
    }
}

function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}

// ========== 测试和调试功能 ==========

function toggleDebugMode() {
    debugMode = !debugMode;
    const status = document.getElementById('debugStatus');
    status.textContent = debugMode ? '开启' : '关闭';
    
    addSystemMessage(`🐛 调试模式已${debugMode ? '开启' : '关闭'}`);
    
    if (debugMode) {
        addSystemMessage('ℹ️ 调试模式将显示详细的协议信息');
    }
}

function quickTestLogin() {
    console.log('快速测试登录被调用');
    
    // 检查环境
    const canUseSCRAM = !!(window.crypto && window.crypto.subtle && window.isSecureContext);
    if (!canUseSCRAM) {
        if (confirm('当前环境不支持SCRAM认证，将使用PLAIN认证。\n建议使用HTTPS或localhost访问以获得最佳安全性。\n\n是否继续？')) {
            // 继续执行
        } else {
            return;
        }
    }
    
    // 先开启调试模式以便查看详细信息
    if (!debugMode) {
        toggleDebugMode();
    }
    
    // 自动填入已知有效的凭据
    document.getElementById('username').value = 'test1';
    document.getElementById('password').value = 'bepa1234';
    document.getElementById('domain').value = 'im-8845.dev.internal.nglock.com';
    
    console.log('凭据已填入，调用login函数');
    
    // 直接调用login函数
    login();
}

function captureScramChallenge() {
    console.log('captureScramChallenge函数被调用');
    
    addSystemMessage('🔍 正在捕获SCRAM挑战用于测试...');
    
    // 开启调试模式
    if (!debugMode) {
        toggleDebugMode();
    }
    
    const username = 'test1';
    const domain = 'im-8845.dev.internal.nglock.com';
    
    try {
        const challengeSocket = new WebSocket(WEBSOCKET_URL);
        
        challengeSocket.onopen = function() {
            addSystemMessage('✓ 挑战捕获连接已建立');
            
            // 发送WebSocket open
            const openStanza = `<open xmlns='urn:ietf:params:xml:ns:xmpp-framing' to='${domain}' version='1.0'/>`;
            challengeSocket.send(openStanza);
            addDebugMessage('挑战捕获发送 ► ' + openStanza);
        };
        
        challengeSocket.onmessage = function(event) {
            addDebugMessage('挑战捕获接收 ◄ ' + event.data);
            
            // 收到WebSocket framing open响应
            if (event.data.includes('<open xmlns=\'urn:ietf:params:xml:ns:xmpp-framing\'')) {
                addSystemMessage('📋 收到WebSocket open响应，等待特性...');
                return;
            }
            
            // 收到特性后发送SCRAM认证请求 - 支持两种格式
            if (event.data.includes('<stream:features') || event.data.includes('<features')) {
                if (event.data.includes('SCRAM-SHA-256')) {
                    addSystemMessage('📋 服务器支持SCRAM-SHA-256，发送初始挑战...');
                    
                    // 生成客户端nonce
                    const clientNonce = generateScramNonce();
                    const initialMessage = `n,,n=${username},r=${clientNonce}`;
                    const initialMessageBase64 = btoa(initialMessage);
                    
                    addSystemMessage(`🔑 客户端随机数: ${clientNonce}`);
                    addSystemMessage(`📤 初始消息: ${initialMessage}`);
                    addSystemMessage(`📤 初始消息(Base64): ${initialMessageBase64}`);
                    
                    const authStanza = `<auth xmlns='urn:ietf:params:xml:ns:xmpp-sasl' mechanism='SCRAM-SHA-256'>${initialMessageBase64}</auth>`;
                    challengeSocket.send(authStanza);
                    addDebugMessage('挑战捕获发送 ► ' + authStanza);
                } else if (event.data.includes('SCRAM-SHA-1')) {
                    addSystemMessage('📋 服务器支持SCRAM-SHA-1，发送初始挑战...');
                    
                    // 生成客户端nonce
                    const clientNonce = generateScramNonce();
                    const initialMessage = `n,,n=${username},r=${clientNonce}`;
                    const initialMessageBase64 = btoa(initialMessage);
                    
                    addSystemMessage(`🔑 客户端随机数: ${clientNonce}`);
                    addSystemMessage(`📤 初始消息: ${initialMessage}`);
                    addSystemMessage(`📤 初始消息(Base64): ${initialMessageBase64}`);
                    
                    const authStanza = `<auth xmlns='urn:ietf:params:xml:ns:xmpp-sasl' mechanism='SCRAM-SHA-1'>${initialMessageBase64}</auth>`;
                    challengeSocket.send(authStanza);
                    addDebugMessage('挑战捕获发送 ► ' + authStanza);
                } else {
                    addSystemMessage('⚠️ 服务器不支持SCRAM认证机制');
                    addSystemMessage('支持的机制: ' + event.data);
                    challengeSocket.close();
                }
            }
            
            // 捕获服务器挑战
            if (event.data.includes('<challenge xmlns=\'urn:ietf:params:xml:ns:xmpp-sasl\'')) {
                const challengeMatch = event.data.match(/<challenge[^>]*>([^<]+)<\/challenge>/);
                if (challengeMatch) {
                    const challengeBase64 = challengeMatch[1];
                    const challengeDecoded = atob(challengeBase64);
                    
                    addSystemMessage('🎯 服务器SCRAM挑战已捕获！');
                    addSystemMessage(`📥 挑战(Base64): ${challengeBase64}`);
                    addSystemMessage(`📥 挑战(解码): ${challengeDecoded}`);
                    
                    // 复制到剪贴板
                    navigator.clipboard.writeText(challengeBase64).then(() => {
                        addSystemMessage('📋 挑战已复制到剪贴板，可以粘贴到测试工具中！');
                    }).catch(() => {
                        addSystemMessage('⚠️ 无法复制到剪贴板，请手动复制上面的Base64挑战');
                    });
                    
                    // 关闭连接
                    challengeSocket.close();
                }
            }
            
            // 处理错误
            if (event.data.includes('<failure xmlns=\'urn:ietf:params:xml:ns:xmpp-sasl\'')) {
                addErrorMessage('❌ SCRAM挑战捕获失败: ' + event.data);
                challengeSocket.close();
            }
        };
        
        challengeSocket.onclose = function() {
            addSystemMessage('📋 挑战捕获连接已关闭');
        };
        
        challengeSocket.onerror = function(error) {
            addErrorMessage('挑战捕获连接错误: ' + error);
        };
        
    } catch (error) {
        addErrorMessage('捕获SCRAM挑战失败: ' + error.message);
    }
}

function tryRegister() {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const domain = document.getElementById('domain').value.trim();
    
    if (!username || !password || !domain) {
        alert('请先填写用户名和密码');
        return;
    }
    
    if (confirm(`是否尝试注册账户 ${username}@${domain}？\n注意：这需要服务器支持在线注册功能。`)) {
        registerAccount(username, password, domain);
    }
}

function registerAccount(username, password, domain) {
    addSystemMessage('🔄 正在尝试注册账户...');
    
    try {
        const regSocket = new WebSocket(WEBSOCKET_URL);
        
        regSocket.onopen = function() {
            addSystemMessage('✓ 注册连接已建立');
            
            // 发送WebSocket open
            const openStanza = `<open xmlns='urn:ietf:params:xml:ns:xmpp-framing' to='${domain}' version='1.0'/>`;
            regSocket.send(openStanza);
        };
        
        regSocket.onmessage = function(event) {
            if (debugMode) {
                addDebugMessage('注册接收 ◄ ' + event.data);
            }
            
            // 处理注册流程
            if (event.data.includes('<open xmlns=\'urn:ietf:params:xml:ns:xmpp-framing\'')) {
                const streamStart = `<stream:stream xmlns='jabber:client' xmlns:stream='http://etherx.jabber.org/streams' to='${domain}' version='1.0'/>`;
                regSocket.send(streamStart);
                
                if (debugMode) {
                    addDebugMessage('注册发送 ► ' + streamStart);
                }
            }
            
            // 收到特性后发送注册请求
            if (event.data.includes('<register')) {
                addSystemMessage('📝 服务器支持注册，发送注册请求...');
                const regId = generateId();
                const registerStanza = `<iq type='set' id='${regId}'><query xmlns='jabber:iq:register'><username>${username}</username><password>${password}</password></query></iq>`;
                regSocket.send(registerStanza);
                
                if (debugMode) {
                    addDebugMessage('注册发送 ► ' + registerStanza);
                }
            }
            
            // 处理注册结果
            if (event.data.includes('<iq') && event.data.includes('type=\'result\'')) {
                addSystemMessage('✅ 账户注册成功！请尝试登录。');
                regSocket.close();
            }
            
            if (event.data.includes('<iq') && event.data.includes('type=\'error\'')) {
                if (event.data.includes('conflict')) {
                    addSystemMessage('ℹ️ 账户已存在，请尝试直接登录。');
                } else {
                    addErrorMessage('❌ 注册失败：' + event.data);
                }
                regSocket.close();
            }
        };
        
        regSocket.onclose = function() {
            addSystemMessage('📋 注册连接已关闭');
        };
        
        regSocket.onerror = function(error) {
            addErrorMessage('注册连接错误: ' + error);
        };
        
    } catch (error) {
        addErrorMessage('注册失败: ' + error.message);
    }
}

// ========== 环境检查和初始化 ==========

function checkEnvironment() {
    const isSecureContext = window.isSecureContext;
    const hasCryptoAPI = !!(window.crypto && window.crypto.subtle);
    const protocol = window.location.protocol;
    const hostname = window.location.hostname;
    
    let envStatus = '';
    let canUseSCRAM = true;
    
    if (!isSecureContext) {
        envStatus += '⚠️ 非安全上下文 ';
        canUseSCRAM = false;
    }
    
    if (!hasCryptoAPI) {
        envStatus += '❌ Web Crypto API不可用 ';
        canUseSCRAM = false;
    }
    
    if (protocol !== 'https:' && hostname !== 'localhost' && hostname !== '127.0.0.1') {
        envStatus += '⚠️ 建议使用HTTPS或localhost ';
    }
    
    if (canUseSCRAM) {
        envStatus = '✅ 环境正常，支持SCRAM认证';
    } else {
        envStatus += ' - 将回退到PLAIN认证';
    }
    
    // 在登录界面显示环境状态
    const loginBox = document.querySelector('.login-box');
    if (loginBox) {
        const envDiv = document.createElement('div');
        envDiv.style.cssText = 'font-size: 12px; color: #666; margin-bottom: 15px; padding: 8px; background: #f8f9fa; border-radius: 4px; text-align: center;';
        envDiv.textContent = envStatus;
        
        // 插入到表单标题后面
        const title = loginBox.querySelector('h2');
        if (title) {
            title.insertAdjacentElement('afterend', envDiv);
        }
    }
    
    return canUseSCRAM;
}

// ========== 事件监听器 ==========

// 页面卸载时关闭连接
window.addEventListener('beforeunload', function() {
    if (websocket) {
        websocket.close();
    }
});

// 🔐 尝试加载保存的加密凭据
async function loadSavedCredentials() {
    try {
        const encryptedCredentials = localStorage.getItem('encryptedCredentials');
        const credentialsSalt = localStorage.getItem('credentialsSalt');
        
        if (!encryptedCredentials || !credentialsSalt) {
            return false;
        }
        
        // 需要用户输入密码来解密
        const password = prompt('检测到保存的登录信息，请输入密码解锁：');
        if (!password) {
            return false;
        }
        
        const encryptionKey = await generateEncryptionKey(password, credentialsSalt);
        const decryptedData = await decryptData(encryptedCredentials, encryptionKey);
        const credentials = JSON.parse(decryptedData);
        
        // 检查凭据时间戳（防止过期凭据）
        const now = Date.now();
        const credentialsAge = now - credentials.timestamp;
        const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
        
        if (credentialsAge > maxAge) {
            if (confirm('保存的登录信息已过期，是否删除？')) {
                localStorage.removeItem('encryptedCredentials');
                localStorage.removeItem('credentialsSalt');
            }
            return false;
        }
        
        // 自动填入解密的凭据
        document.getElementById('username').value = credentials.username;
        document.getElementById('domain').value = credentials.domain;
        document.getElementById('password').focus(); // 仍需手动输入密码
        
        addSystemMessage('🔓 已加载保存的登录信息');
        addSystemMessage(`👤 用户名: ${credentials.username}@${credentials.domain}`);
        addSystemMessage('🔑 请输入密码完成登录');
        
        return true;
        
    } catch (error) {
        console.error('加载保存凭据失败:', error);
        addSystemMessage('⚠️ 无法加载保存的登录信息');
        
        // 清理无效的存储数据
        localStorage.removeItem('encryptedCredentials');
        localStorage.removeItem('credentialsSalt');
        return false;
    }
}

// 🔐 会话超时检查
function checkSessionTimeout() {
    if (isAuthenticated && securityConfig.sessionStartTime) {
        if (!isSessionValid()) {
            addErrorMessage('⏰ 会话已超时，为了安全起见已自动登出');
            secureLogout();
            return;
        }
        
        // 检查即将超时的会话（剩余10分钟时提醒）
        const now = Date.now();
        const timeRemaining = securityConfig.sessionTimeout - (now - securityConfig.sessionStartTime);
        const warningTime = 10 * 60 * 1000; // 10分钟
        
        if (timeRemaining <= warningTime && timeRemaining > 0) {
            const minutesRemaining = Math.ceil(timeRemaining / 60000);
            if (minutesRemaining === 10 || minutesRemaining === 5 || minutesRemaining === 1) {
                addSystemMessage(`⏰ 会话将在 ${minutesRemaining} 分钟后超时`);
            }
        }
    }
}

// 🔐 安全配置UI
function showSecuritySettings() {
    const settings = `
当前安全设置:
• 密码最小长度: ${securityConfig.minimumPasswordLength} 字符
• 强密码要求: ${securityConfig.requireStrongPassword ? '启用' : '禁用'}
• 最大登录尝试: ${securityConfig.maxLoginAttempts} 次
• 锁定时间: ${securityConfig.lockoutTime / 60000} 分钟
• 会话超时: ${securityConfig.sessionTimeout / 60000} 分钟
• 加密存储: ${securityConfig.encryptionEnabled ? '启用' : '禁用'}

是否要修改安全设置？
    `;
    
    if (confirm(settings)) {
        const newMinLength = parseInt(prompt('设置密码最小长度(6-32):', securityConfig.minimumPasswordLength));
        if (newMinLength >= 6 && newMinLength <= 32) {
            securityConfig.minimumPasswordLength = newMinLength;
        }
        
        securityConfig.requireStrongPassword = confirm('是否要求强密码（包含大小写字母、数字和特殊字符）？\n\n选择"取消"将允许简单密码，仅显示强度评分作为建议。');
        
        const newMaxAttempts = parseInt(prompt('设置最大登录尝试次数(3-10):', securityConfig.maxLoginAttempts));
        if (newMaxAttempts >= 3 && newMaxAttempts <= 10) {
            securityConfig.maxLoginAttempts = newMaxAttempts;
        }
        
        addSystemMessage('✅ 安全设置已更新');
        addSystemMessage(`💡 强密码要求: ${securityConfig.requireStrongPassword ? '已启用' : '已禁用'}`);
        addSystemMessage(`💡 最小密码长度: ${securityConfig.minimumPasswordLength} 字符`);
    }
}

// 🔐 快速切换密码策略
function togglePasswordPolicy() {
    const current = securityConfig.requireStrongPassword ? '严格' : '宽松';
    const newPolicy = securityConfig.requireStrongPassword ? '宽松' : '严格';
    
    if (confirm(`当前密码策略: ${current}\n\n是否切换到${newPolicy}策略？\n\n严格: 必须包含大小写字母、数字、特殊字符\n宽松: 最低6位字符即可`)) {
        securityConfig.requireStrongPassword = !securityConfig.requireStrongPassword;
        addSystemMessage(`✅ 密码策略已切换为: ${securityConfig.requireStrongPassword ? '严格' : '宽松'}`);
        
        // 更新全局变量
        window.updateGlobalDebugVars();
    }
}

// 🔐 防止重复初始化的标志
let isInitialized = false;

// 页面加载完成后检查环境和初始化
window.addEventListener('DOMContentLoaded', function() {
    if (isInitialized) {
        console.log('⚠️ 已经初始化过，跳过重复初始化');
        return;
    }
    isInitialized = true;
    
    console.log('🔐 DOM加载完成，开始初始化安全登录功能...');
    
    checkEnvironment();
    
    // 表单提交处理
    const loginForm = document.getElementById('loginFormElement');
    if (loginForm) {
        console.log('✅ 找到登录表单，设置提交事件监听器');
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('🔐 表单提交事件触发，调用login函数');
            
            try {
                login();
            } catch (error) {
                console.error('❌ 登录函数执行出错:', error);
                addErrorMessage('登录功能出错: ' + error.message);
            }
        });
    } else {
        console.error('❌ 未找到登录表单元素(id: loginFormElement)');
    }
    
    // 🔐 尝试加载保存的凭据
    setTimeout(() => {
        console.log('🔑 尝试加载保存的凭据...');
        loadSavedCredentials();
    }, 500);
    
    // 🔐 定期检查会话超时（只设置一次）
    if (!window.sessionTimeoutInterval) {
        window.sessionTimeoutInterval = setInterval(checkSessionTimeout, 60000); // 每分钟检查一次
    }
    
    // 添加调试按钮功能检查
    setTimeout(() => {
        console.log('🔍 检查所有安全功能是否就绪:');
        console.log('- securityConfig:', typeof securityConfig !== 'undefined' ? '✅' : '❌');
        console.log('- login函数:', typeof login === 'function' ? '✅' : '❌');
        console.log('- quickTestLogin函数:', typeof quickTestLogin === 'function' ? '✅' : '❌');
        console.log('- Web Crypto API:', !!(window.crypto && window.crypto.subtle) ? '✅' : '❌');
        console.log('- 安全上下文:', window.isSecureContext ? '✅' : '❌');
        
        // 🔐 更新全局调试变量
        window.updateGlobalDebugVars();
    }, 1000);
});

// 页面卸载时安全清理
window.addEventListener('beforeunload', function() {
    if (websocket) {
        // 发送快速下线状态
        if (websocket.readyState === WebSocket.OPEN && isAuthenticated) {
            websocket.send('<presence type="unavailable"/>');
        }
        websocket.close();
    }
    
    // 清理敏感数据
    clearSession();
});

// 🔐 设置默认测试凭据
function setDefaultCredentials() {
    try {
        const usernameEl = document.getElementById('username') || document.getElementById('hiddenUsername');
        const passwordEl = document.getElementById('password') || document.getElementById('hiddenPassword');
        const domainEl = document.getElementById('domain') || document.getElementById('hiddenDomain');
        
        if (usernameEl && !usernameEl.value) {
            usernameEl.value = 'test1';
        }
        if (passwordEl && !passwordEl.value) {
            passwordEl.value = 'bepa1234';
        }
        if (domainEl && !domainEl.value) {
            domainEl.value = 'im-8845.dev.internal.nglock.com';
        }
        
        console.log('✅ 已设置默认测试凭据');
    } catch (error) {
        console.error('设置默认凭据失败:', error);
    }
}

// 🔐 暴露关键变量到全局作用域以便调试
// 直接赋值方式（更简单可靠）
window.securityConfig = securityConfig;
window.scramState = scramState;
window.loginCredentials = loginCredentials;
window.isAuthenticated = isAuthenticated;
window.currentJid = currentJid;
window.debugMode = debugMode;
window.setDefaultCredentials = setDefaultCredentials;
window.togglePasswordPolicy = togglePasswordPolicy;
window.showSecuritySettings = showSecuritySettings;

// 添加更新函数以保持同步
window.updateGlobalDebugVars = function() {
    window.securityConfig = securityConfig;
    window.scramState = scramState;
    window.loginCredentials = loginCredentials;
    window.isAuthenticated = isAuthenticated;
    window.currentJid = currentJid;
    window.debugMode = debugMode;
}; 