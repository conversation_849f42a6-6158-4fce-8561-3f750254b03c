<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💬 IM-Tiny - 极简聊天</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            min-height: 600px;
        }
        
        /* 登录界面 */
        .login-screen {
            padding: 40px 30px;
            text-align: center;
        }
        
        .login-screen h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 300;
        }
        
        .input-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            color: #666;
            font-size: 14px;
        }
        
        .input-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .input-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .login-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
        }
        
        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
            text-align: center;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        /* 聊天界面 */
        .chat-screen {
            display: none;
            height: 600px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chat-header h2 {
            font-size: 18px;
            font-weight: 500;
        }
        
        .chat-header .status-indicator {
            display: flex;
            align-items: center;
            font-size: 12px;
        }
        
        .chat-header .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }
        
        .status-online { background: #28a745; }
        .status-connecting { background: #ffc107; }
        .status-offline { background: #6c757d; }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            max-width: 80%;
        }
        
        .message.sent {
            margin-left: auto;
        }
        
        .message.received {
            margin-right: auto;
        }
        
        .message-bubble {
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.sent .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .message.received .message-bubble {
            background: white;
            color: #333;
            border: 1px solid #e1e5e9;
        }
        
        .message.system .message-bubble {
            background: #fff3cd;
            color: #856404;
            text-align: center;
            border-radius: 12px;
            font-size: 13px;
            margin: 0 auto;
        }
        
        .message-time {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 4px;
            text-align: center;
        }
        
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            gap: 10px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 20px;
            font-size: 14px;
            outline: none;
        }
        
        .chat-input input:focus {
            border-color: #667eea;
        }
        
        .send-btn {
            width: 44px;
            height: 44px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;
        }
        
        .send-btn:hover {
            transform: scale(1.05);
        }
        
        .logout-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 14px;
            opacity: 0.8;
            transition: opacity 0.2s;
        }
        
        .logout-btn:hover {
            opacity: 1;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            padding: 5px 10px;
            margin-right: 10px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .back-btn:hover {
            background-color: rgba(255,255,255,0.1);
        }
        
        /* 通信录样式 */
        .contacts-list {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .welcome-message {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .welcome-message h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .welcome-message p {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
        
        .add-contact-section {
            margin-bottom: 20px;
            text-align: center;
        }
        
        .add-contact-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .add-contact-btn:hover {
            transform: translateY(-2px);
        }
        
        .contact-item {
            background: white;
            margin-bottom: 10px;
            padding: 16px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
        }
        
        .contact-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .contact-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 15px;
            font-size: 16px;
        }
        
        .contact-info {
            flex: 1;
        }
        
        .contact-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
        }
        
        .contact-status {
            font-size: 12px;
            color: #666;
            display: flex;
            align-items: center;
        }
        
        .contact-status .status-indicator {
            margin-right: 5px;
        }
        
        /* 隐藏类 */
        .hidden {
            display: none !important;
        }
        
        /* 响应式 */
        @media (max-width: 480px) {
            .container {
                max-width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .chat-screen {
                height: 100vh;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录界面 -->
        <div id="loginScreen" class="login-screen">
            <h1>💬 IM-Tiny</h1>
            <form id="loginForm">
                <div class="input-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" required>
                </div>
                <div class="input-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" required>
                </div>
                <div class="input-group">
                    <label for="domain">服务器</label>
                    <input type="text" id="domain" value="im-8845.dev.internal.nglock.com" required>
                </div>
                <button type="submit" id="loginBtn" class="login-btn">
                    🔐 登录
                </button>
            </form>
            <div id="loginStatus" class="status hidden"></div>
            
            <!-- 快速设置按钮 -->
            <div style="margin-top: 20px; text-align: center;">
                <button type="button" onclick="handleTogglePasswordPolicy()" style="background: none; border: 1px solid #ddd; padding: 8px 12px; border-radius: 4px; font-size: 12px; color: #666; cursor: pointer;">
                    💡 切换密码策略
                </button>
            </div>
        </div>
        
        <!-- 通信录界面 -->
        <div id="contactsScreen" class="chat-screen hidden">
            <div class="chat-header">
                <h2>📱 通信录</h2>
                <div>
                    <span class="status-indicator">
                        <span id="statusDot" class="status-dot status-offline"></span>
                        <span id="statusText">离线</span>
                    </span>
                    <button class="logout-btn" onclick="logout()">登出</button>
                </div>
            </div>
            
            <div id="contactsList" class="contacts-list">
                <div class="welcome-message">
                    <h3>🎉 欢迎使用 IM-Tiny！</h3>
                    <p>您的联系人将在下方显示</p>
                </div>
                
                <!-- 添加联系人按钮 -->
                <div class="add-contact-section">
                    <button class="add-contact-btn" onclick="showAddContactDialog()">
                        ➕ 添加联系人
                    </button>
                </div>
                
                <!-- 联系人列表将在这里动态添加 -->
                <div id="contactsListContent"></div>
            </div>
        </div>

        <!-- 聊天界面 -->
        <div id="chatScreen" class="chat-screen hidden">
            <div class="chat-header">
                <button class="back-btn" onclick="backToContacts()">‹</button>
                <h2 id="chatTitle">选择联系人</h2>
                <div>
                    <span class="status-indicator">
                        <span id="statusDot2" class="status-dot status-offline"></span>
                        <span id="statusText2">离线</span>
                    </span>
                </div>
            </div>
            
            <div id="chatMessages" class="chat-messages">
                <!-- 消息将在这里动态添加 -->
            </div>
            
            <div class="chat-input">
                <input type="text" id="messageInput" placeholder="输入消息..." maxlength="500">
                <button id="sendBtn" class="send-btn" onclick="sendMessage()">
                    ➤
                </button>
            </div>
        </div>
    </div>

    <!-- 隐藏的表单元素，用于兼容现有的 im-client.js -->
    <div style="display: none;">
        <form id="loginFormElement">
            <input type="text" id="hiddenUsername">
            <input type="password" id="hiddenPassword">
            <input type="text" id="hiddenDomain">
            <input type="checkbox" id="rememberMe">
        </form>
        <div id="chatApp" class="hidden">
            <div id="currentUser"></div>
            <div id="connectionStatus"></div>
            <div id="userStatusIndicator"></div>
            <div id="userStatusText"></div>
            <div id="chatMessagesHidden"></div>
            <input type="text" id="messageInputHidden">
            <button id="reconnectBtn" class="hidden"></button>
            <div id="contactsList"></div>
        </div>
    </div>

    <script>
        // IM-Tiny 专用变量
        let currentScreen = 'login';
        let isConnected = false;
        let messageHistory = [];
        let contactsList = [];
        let currentContactTiny = null; // 重命名避免与im-client.js冲突
        let allMessages = {}; // 存储所有联系人的消息
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('💬 IM-Tiny 启动');
            
            // 设置默认凭据（用于测试）
            document.getElementById('username').value = 'test1';
            document.getElementById('password').value = 'bepa1234';
            
            // 绑定事件
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    console.log('⌨️ 回车键被按下 - 调用IM-Tiny的sendMessage');
                    sendMessageTiny();
                }
            });
            
            // 覆盖原有的UI函数以适配新界面
            window.addSystemMessage = addSystemMessageTiny;
            window.addErrorMessage = addErrorMessageTiny;
            window.updateConnectionStatus = updateConnectionStatusTiny;
            window.displayMessages = displayMessagesTiny;
            window.selectContact = function() {}; // 禁用原联系人选择
            window.addContactToList = addContactToListTiny; // 覆盖添加联系人函数
            
            // 初始化currentContactJid为非系统值，避免错误
            window.currentContactJid = '';
            window.currentContact = '';
        });
        
        // 登录处理
        function handleLogin(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const domain = document.getElementById('domain').value.trim();
            
            if (!username || !password || !domain) {
                showLoginStatus('请填写完整的登录信息', 'error');
                return;
            }
            
            // 显示登录状态
            showLoginStatus('🔐 正在连接...', 'info');
            document.getElementById('loginBtn').disabled = true;
            document.getElementById('loginBtn').textContent = '连接中...';
            
            // 同步数据到隐藏表单（兼容现有代码）
            document.getElementById('hiddenUsername').value = username;
            document.getElementById('hiddenPassword').value = password;
            document.getElementById('hiddenDomain').value = domain;
            
            // 等待并调用现有的登录函数
            waitForLoginFunction();
        }
        
        // 等待login函数加载
        function waitForLoginFunction() {
            if (typeof window.login === 'function') {
                try {
                    console.log('✅ login函数已加载，开始登录');
                    
                    // 现在覆盖sendMessage函数，确保im-client.js已加载完毕
                    window.sendMessage = sendMessageTiny;
                    console.log('✅ sendMessage函数已覆盖');
                    
                    window.login();
                } catch (error) {
                    console.error('登录错误:', error);
                    showLoginStatus('登录失败: ' + error.message, 'error');
                    resetLoginButton();
                }
            } else {
                console.log('⏳ 等待login函数加载...');
                setTimeout(waitForLoginFunction, 100);
            }
        }
        
        // 处理密码策略切换
        function handleTogglePasswordPolicy() {
            if (typeof window.togglePasswordPolicy === 'function') {
                window.togglePasswordPolicy();
                showLoginStatus('密码策略已切换', 'info');
                setTimeout(() => {
                    document.getElementById('loginStatus').classList.add('hidden');
                }, 2000);
            } else {
                console.log('togglePasswordPolicy函数尚未加载');
            }
        }
        
        // 显示登录状态
        function showLoginStatus(message, type) {
            const status = document.getElementById('loginStatus');
            status.textContent = message;
            status.className = `status ${type}`;
            status.classList.remove('hidden');
        }
        
        // 重置登录按钮
        function resetLoginButton() {
            document.getElementById('loginBtn').disabled = false;
            document.getElementById('loginBtn').textContent = '🔐 登录';
        }
        
        // 切换到通信录界面
        function switchToContactsScreen() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('contactsScreen').classList.remove('hidden');
            document.getElementById('chatScreen').classList.add('hidden');
            currentScreen = 'contacts';
            
            // 更新连接状态指示器
            updateContactsStatusDisplay();
            
            // 初始化通信录
            initializeContacts();
            
            // 再次强制覆盖sendMessage函数（防止被其他代码重置）
            window.sendMessage = sendMessageTiny;
            console.log('🔄 在通信录界面再次覆盖sendMessage函数');
        }
        
        // 切换到聊天界面
        function switchToChatScreen(contact) {
            document.getElementById('contactsScreen').classList.add('hidden');
            document.getElementById('chatScreen').classList.remove('hidden');
            currentScreen = 'chat';
            currentContactTiny = contact;
            
            // 同步到im-client.js的变量（重要！）
            window.currentContact = contact.name;
            window.currentContactJid = contact.jid;
            
            // 再次强制覆盖sendMessage函数并确认设置
            window.sendMessage = sendMessageTiny;
            console.log('🔄 在聊天界面再次覆盖sendMessage函数');
            console.log('📍 当前联系人JID:', contact.jid);
            console.log('📍 window.currentContactJid:', window.currentContactJid);
            
            // 更新聊天标题
            document.getElementById('chatTitle').textContent = contact.name;
            
            // 更新连接状态指示器
            updateChatStatusDisplay();
            
            // 显示该联系人的消息历史
            displayContactMessages(contact.jid);
            
            // 聚焦到输入框
            setTimeout(() => {
                document.getElementById('messageInput').focus();
                
                // 重新绑定发送按钮的点击事件（覆盖onclick属性）
                const sendBtn = document.getElementById('sendBtn');
                if (sendBtn) {
                    // 移除原有的onclick属性
                    sendBtn.removeAttribute('onclick');
                    // 移除所有现有的事件监听器
                    sendBtn.replaceWith(sendBtn.cloneNode(true));
                    // 重新获取按钮元素并添加新的事件监听器
                    const newSendBtn = document.getElementById('sendBtn');
                    newSendBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        console.log('🔘 发送按钮被点击 - 调用IM-Tiny的sendMessage');
                        // 直接调用本地函数，避免全局函数查找
                        sendMessageTiny();
                    });
                    console.log('🔄 发送按钮事件已重新绑定');
                }
            }, 100);
        }
        
        // 返回通信录
        function backToContacts() {
            switchToContactsScreen();
        }
        
        // 适配的系统消息函数
        function addSystemMessageTiny(text) {
            console.log('系统消息:', text);
            
            // 检查登录成功的标志
            if (text.includes('登录成功') || text.includes('现在可以开始聊天')) {
                switchToContactsScreen();
                updateConnectionStatusTiny('已连接', 'online');
                return;
            }
            
            // 过滤掉技术性系统消息（登录成功后）
            if (currentScreen === 'chat') {
                // 只显示重要的用户消息，过滤技术细节
                const importantMessages = [
                    '收到来自',
                    '发送消息',
                    '连接已断开',
                    '重新连接',
                    '❌',
                    '⚠️'
                ];
                
                const shouldShow = importantMessages.some(keyword => text.includes(keyword));
                if (shouldShow) {
                    addMessageToChat(text, 'system');
                }
                return;
            }
            
            // 登录界面仍显示所有消息
            if (currentScreen === 'login') {
                addMessageToChat(text, 'system');
            }
        }
        
        // 适配的错误消息函数
        function addErrorMessageTiny(text) {
            console.log('错误消息:', text);
            if (currentScreen === 'login') {
                showLoginStatus(text, 'error');
                resetLoginButton();
            } else {
                // 聊天界面中只显示重要的错误消息
                const importantErrors = [
                    '连接已断开',
                    '发送失败',
                    '认证失败',
                    '网络错误',
                    '服务器错误'
                ];
                
                const shouldShow = importantErrors.some(keyword => text.includes(keyword));
                if (shouldShow) {
                    addMessageToChat(text, 'system');
                }
            }
        }
        
        // 适配的连接状态更新函数
        function updateConnectionStatusTiny(status, type) {
            console.log('连接状态:', status, type);
            
            isConnected = (type === 'online');
            
            // 更新通信录界面的状态
            updateContactsStatusDisplay();
            
            // 更新聊天界面的状态
            updateChatStatusDisplay();
            
            // 更新发送按钮状态
            const sendBtn = document.getElementById('sendBtn');
            if (sendBtn) {
                sendBtn.style.opacity = isConnected ? '1' : '0.5';
            }
        }
        
        // 更新通信录状态显示
        function updateContactsStatusDisplay() {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            if (statusDot && statusText) {
                const statusType = isConnected ? 'online' : 'offline';
                const statusTextValue = isConnected ? '在线' : '离线';
                
                statusDot.className = `status-dot status-${statusType}`;
                statusText.textContent = statusTextValue;
            }
        }
        
        // 更新聊天状态显示
        function updateChatStatusDisplay() {
            const statusDot = document.getElementById('statusDot2');
            const statusText = document.getElementById('statusText2');
            
            if (statusDot && statusText) {
                const statusType = isConnected ? 'online' : 'offline';
                const statusTextValue = isConnected ? '在线' : '离线';
                
                statusDot.className = `status-dot status-${statusType}`;
                statusText.textContent = statusTextValue;
            }
        }
        
        // 适配的消息显示函数
        function displayMessagesTiny() {
            // 这个函数被现有代码调用，但我们使用自己的消息系统
        }
        
        // 添加消息到聊天界面
        function addMessageToChat(text, type = 'received', time = null) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            const timeStr = time || new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit', 
                minute: '2-digit'
            });
            
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `
                <div class="message-bubble">${escapeHtml(text)}</div>
                <div class="message-time">${timeStr}</div>
            `;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
            
            // 保存到历史记录
            messageHistory.push({text, type, time: timeStr});
        }
        
        // 初始化通信录
        function initializeContacts() {
            // 添加一些默认的联系人用于演示
            if (contactsList.length === 0) {
                addDefaultContacts();
            }
            renderContactsList();
        }
        
        // 添加默认联系人
        function addDefaultContacts() {
            const defaultContacts = [
                { jid: '<EMAIL>', name: '测试用户2', online: true },
                { jid: '<EMAIL>', name: '管理员', online: false },
                { jid: '<EMAIL>', name: '技术支持', online: true }
            ];
            
            defaultContacts.forEach(contact => {
                if (!contactsList.find(c => c.jid === contact.jid)) {
                    contactsList.push(contact);
                    allMessages[contact.jid] = [];
                }
            });
        }
        
        // 渲染通信录列表
        function renderContactsList() {
            const container = document.getElementById('contactsListContent');
            container.innerHTML = '';
            
            contactsList.forEach(contact => {
                const contactDiv = document.createElement('div');
                contactDiv.className = 'contact-item';
                contactDiv.onclick = () => switchToChatScreen(contact);
                
                const avatar = contact.name.charAt(0).toUpperCase();
                const statusClass = contact.online ? 'status-online' : 'status-offline';
                const statusText = contact.online ? '在线' : '离线';
                
                contactDiv.innerHTML = `
                    <div class="contact-avatar">${avatar}</div>
                    <div class="contact-info">
                        <div class="contact-name">${contact.name}</div>
                        <div class="contact-status">
                            <span class="status-indicator">
                                <span class="status-dot ${statusClass}"></span>
                                ${statusText}
                            </span>
                        </div>
                    </div>
                `;
                
                container.appendChild(contactDiv);
            });
        }
        
        // 添加联系人到列表（适配现有函数）
        function addContactToListTiny(jid, displayName) {
            const name = displayName || jid.split('@')[0];
            
            if (!contactsList.find(c => c.jid === jid)) {
                contactsList.push({
                    jid: jid,
                    name: name,
                    online: true
                });
                
                allMessages[jid] = [];
                renderContactsList();
            }
        }
        
        // 显示添加联系人对话框
        function showAddContactDialog() {
            const jid = prompt('请输入联系人JID (例如: <EMAIL>):');
            if (jid && jid.includes('@')) {
                const name = prompt('请输入联系人姓名:', jid.split('@')[0]);
                if (name) {
                    addContactToListTiny(jid, name);
                    
                    // 发送订阅请求（如果已连接）
                    if (isConnected && typeof window.addContact === 'function') {
                        try {
                            window.addContact(jid);
                        } catch (error) {
                            console.error('发送好友请求失败:', error);
                        }
                    }
                }
            }
        }
        
        // 显示联系人的消息历史
        function displayContactMessages(jid) {
            console.log('🔄 displayContactMessages被调用，JID:', jid);
            console.log('📱 当前消息数量:', (allMessages[jid] || []).length);
            
            const chatMessages = document.getElementById('chatMessages');
            const previousMessageCount = chatMessages.children.length;
            console.log('📱 清空前界面消息数量:', previousMessageCount);
            
            chatMessages.innerHTML = '';
            
            const messages = allMessages[jid] || [];
            console.log('📱 要显示的消息:', messages);
            
            messages.forEach((msg, index) => {
                console.log(`📱 渲染消息 ${index + 1}:`, msg);
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${msg.type}`;
                messageDiv.innerHTML = `
                    <div class="message-bubble">${escapeHtml(msg.text)}</div>
                    <div class="message-time">${msg.time}</div>
                `;
                chatMessages.appendChild(messageDiv);
            });
            
            console.log('📱 渲染完成，界面消息数量:', chatMessages.children.length);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 发送消息
        function sendMessageTiny() {
            console.log('🚀 IM-Tiny sendMessageTiny被调用 - 这是IM-Tiny的函数！');
            console.log('🔍 函数调用栈:', new Error().stack);
            
            const input = document.getElementById('messageInput');
            const text = input.value.trim();
            
            console.log('📝 发送消息参数:', {
                text: text,
                isConnected: isConnected,
                currentContactTiny: currentContactTiny,
                currentContactJid: window.currentContactJid,
                currentContact: window.currentContact
            });
            
            if (!text || !isConnected || !currentContactTiny) {
                console.log('❌ 发送消息条件不满足');
                return;
            }
            
            const time = new Date().toLocaleTimeString('zh-CN', {
                hour: '2-digit', 
                minute: '2-digit'
            });
            
            // 添加到当前联系人的消息历史
            if (!allMessages[currentContactTiny.jid]) {
                allMessages[currentContactTiny.jid] = [];
            }
            
            console.log('📝 添加消息到历史记录前，当前消息数量:', allMessages[currentContactTiny.jid].length);
            
            allMessages[currentContactTiny.jid].push({
                text: text,
                type: 'sent',
                time: time
            });
            
            console.log('📝 添加消息到历史记录后，当前消息数量:', allMessages[currentContactTiny.jid].length);
            console.log('📝 最后一条消息:', allMessages[currentContactTiny.jid][allMessages[currentContactTiny.jid].length - 1]);
            
            // 刷新聊天显示
            console.log('🔄 调用displayContactMessages刷新聊天显示');
            displayContactMessages(currentContactTiny.jid);
            input.value = '';
            
            // 发送XMPP消息
            try {
                // 设置当前联系人JID到全局变量
                window.currentContactJid = currentContactTiny.jid;
                
                // 详细检查WebSocket和认证状态
                // im-client.js中的变量是局部变量，需要通过其他方式访问
                // 尝试从DOM元素获取当前用户JID
                const currentUserElement = document.getElementById('currentUser');
                const actualJid = currentUserElement ? currentUserElement.textContent : '';
                
                console.log('🔍 WebSocket状态检查:', {
                    'window.websocket': window.websocket,
                    'window.isAuthenticated': window.isAuthenticated,
                    'isConnected': isConnected,
                    'window.currentJid': window.currentJid,
                    'DOM中的actualJid': actualJid,
                    'loginCredentials存在': !!window.loginCredentials
                });
                
                // 如果window.currentJid为空，但actualJid有值，更新window.currentJid
                if (!window.currentJid && actualJid) {
                    window.currentJid = actualJid;
                    console.log('🔄 更新window.currentJid为:', actualJid);
                }
                
                // 使用isConnected状态和实际JID替代直接检查websocket和isAuthenticated
                if (isConnected && (window.currentJid || actualJid)) {
                    // 设置隐藏输入框的值（兼容原有代码）
                    const hiddenInput = document.getElementById('messageInputHidden');
                    if (hiddenInput) {
                        hiddenInput.value = text;
                    }
                    
                    // 直接构造和发送XMPP消息，模拟im-client.js的sendMessage函数
                    try {
                        const messageId = Math.random().toString(36).substr(2, 9);
                        const time = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
                        
                        // 构造XMPP消息（模拟im-client.js的实现）
                        const messageStanza = `<message type='chat' to='${currentContactTiny.jid}' id='${messageId}'><body>${escapeHtml(text)}</body></message>`;
                        
                        // 通过eval访问im-client.js的websocket变量并发送消息
                        const sendResult = eval(`
                            (function() {
                                try {
                                    if (typeof websocket !== 'undefined' && websocket && websocket.readyState === WebSocket.OPEN && typeof isAuthenticated !== 'undefined' && isAuthenticated) {
                                        websocket.send('${messageStanza.replace(/'/g, "\\'")}');
                                        return {success: true, message: '消息发送成功'};
                                    } else {
                                        return {success: false, message: 'WebSocket未连接或未认证'};
                                    }
                                } catch (e) {
                                    return {success: false, message: e.message};
                                }
                            })()
                        `);
                        
                        if (sendResult.success) {
                            console.log('✅ 消息发送成功:', text);
                            
                            // 消息已经在sendMessageTiny开始时添加到allMessages并显示了
                            // 不需要重复添加
                            
                        } else {
                            throw new Error(sendResult.message);
                        }
                        
                    } catch (error) {
                        console.error('❌ 发送消息失败:', error);
                        addMessageToChat('❌ 发送失败: ' + error.message, 'system');
                    }
                } else {
                    console.error('❌ 未连接或未认证');
                    console.error('详细状态:', {
                        isConnected: isConnected,
                        currentJid: window.currentJid
                    });
                    addMessageToChat('❌ 连接断开，请重新登录', 'system');
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessageToChat('❌ 发送失败，请重试', 'system');
            }
        }
        
        // 登出
        function logout() {
            if (confirm('确定要登出吗？')) {
                // 调用现有的登出函数
                if (typeof window.secureLogout === 'function') {
                    window.secureLogout();
                }
                
                // 重置界面
                document.getElementById('chatScreen').classList.add('hidden');
                document.getElementById('contactsScreen').classList.add('hidden');
                document.getElementById('loginScreen').classList.remove('hidden');
                document.getElementById('chatMessages').innerHTML = '';
                document.getElementById('loginStatus').classList.add('hidden');
                
                // 重置数据
                messageHistory = [];
                contactsList = [];
                currentContactTiny = null;
                allMessages = {};
                currentScreen = 'login';
                isConnected = false;
                
                resetLoginButton();
                updateConnectionStatusTiny('离线', 'offline');
            }
        }
        
        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // 覆盖现有的函数以兼容
        window.handleKeyPress = function(event) {
            if (event.key === 'Enter') {
                sendMessageTiny();
            }
        };
        
        // 处理接收到的消息
        function handleReceivedMessage(fromJid, messageText, time) {
            // 确保联系人存在于列表中
            addContactToListTiny(fromJid);
            
            // 添加消息到对应联系人的历史记录
            if (!allMessages[fromJid]) {
                allMessages[fromJid] = [];
            }
            
            allMessages[fromJid].push({
                text: messageText,
                type: 'received',
                time: time
            });
            
            // 如果当前正在与该联系人聊天，刷新显示
            if (currentContactTiny && currentContactTiny.jid === fromJid) {
                displayContactMessages(fromJid);
            }
            
            // 更新通信录（可以添加未读消息提示等）
            renderContactsList();
        }
        
        // 覆盖全局的消息处理函数
        window.handleReceivedMessageTiny = handleReceivedMessage;
        
        // 监听现有代码的消息事件
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'xmpp-message') {
                const time = new Date().toLocaleTimeString('zh-CN', {
                    hour: '2-digit', 
                    minute: '2-digit'
                });
                handleReceivedMessage(event.data.from, event.data.text, time);
            }
        });
    </script>
    
    <!-- 加载现有的 IM 客户端 -->
    <script src="im-client.js"></script>
</body>
</html> 